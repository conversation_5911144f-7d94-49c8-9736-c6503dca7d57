#!/usr/bin/env python3
"""
Test script for the enhanced agent with 100% tool usage.
"""

import asyncio
import sys
import os

# Add the app directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agent import EfficientAgentOnlyJudgeWithCSS

async def test_enhanced_agent():
    """Test the enhanced agent with a simple HTML example."""
    
    # Simple test HTML with various link types
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test Page</title>
    </head>
    <body>
        <nav>
            <a href="/home" class="nav-link">Home</a>
            <a href="/products" class="nav-link">Products</a>
            <a href="/about" class="nav-link">About</a>
        </nav>
        
        <main>
            <h1>Welcome to Our Store</h1>
            <p>Check out our amazing products!</p>
            
            <a href="/featured-product" class="btn btn-primary cta">Shop Now</a>
            <a href="/newsletter" class="btn btn-secondary">Subscribe</a>
            <a href="/contact" class="contact-link">Contact Us</a>
            <a href="/blog/latest-post" class="content-link">Read Our Latest Blog</a>
        </main>
        
        <footer>
            <a href="/privacy" class="footer-link">Privacy Policy</a>
            <a href="/terms" class="footer-link">Terms of Service</a>
        </footer>
    </body>
    </html>
    """
    
    try:
        print("🚀 Initializing Enhanced Agent with 100% Tool Usage...")
        agent = EfficientAgentOnlyJudgeWithCSS()
        
        print("🔍 Testing link selection with enhanced tool-driven approach...")
        result = await agent.select_prefetch_links(test_html, "https://example.com")
        
        print("\n" + "="*80)
        print("📊 ENHANCED AGENT RESULTS")
        print("="*80)
        
        if "error" in result:
            print(f"❌ Error: {result['error']}")
            return
        
        print(f"🔗 Total links found: {result.get('total_links', 'N/A')}")
        print(f"🎯 Links selected: {len(result.get('selected_links', []))}")
        print(f"🏆 Confidence score: {result.get('confidence_score', 'N/A')}")
        print(f"🔒 Consistency hash: {result.get('consistency_hash', 'N/A')}")
        
        print("\n📋 SELECTED LINKS:")
        for i, link in enumerate(result.get('selected_links', []), 1):
            print(f"{i}. URL: {link.get('url', 'N/A')}")
            print(f"   Score: {link.get('score', 'N/A')}")
            print(f"   Reason: {link.get('individual_reason', 'N/A')}")
            print(f"   Location: {link.get('location', 'N/A')}")
            print(f"   Button Type: {link.get('button_type', 'N/A')}")
            print()
        
        print("🧠 OVERALL REASONING:")
        print(result.get('reasoning', 'No reasoning provided'))
        
        print("\n🔧 RAG CONTEXT USAGE:")
        print(result.get('rag_context_used', 'N/A'))
        
        print("\n✅ Enhanced agent test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🎯 Testing Enhanced Agent with 100% Tool Usage")
    print("="*60)
    asyncio.run(test_enhanced_agent())
