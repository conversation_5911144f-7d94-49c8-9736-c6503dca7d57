{"selected_links": [{"url": "https://www.langchain.com/langgraph", "text": "LangGraph", "title": "", "rel": "", "position": 1, "location": "navigation", "button_type": "premium", "css_data": {"classes": ["navbar_navlink"], "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".navbar_navlink", "style": "color: var(--colors--green--green-400);\nfont-size: 1.25rem;\ntext-decoration: none"}]}, "score": 0.95, "individual_reason": "Premium button in navigation, likely a key product page."}, {"url": "https://www.langchain.com/langchain", "text": "<PERSON><PERSON><PERSON><PERSON>", "title": "", "rel": "", "position": 2, "location": "navigation", "button_type": "premium", "css_data": {"classes": ["navbar_navlink"], "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".navbar_navlink", "style": "color: var(--colors--green--green-400);\nfont-size: 1.25rem;\ntext-decoration: none"}]}, "score": 0.95, "individual_reason": "Premium button in navigation, likely a key product page."}, {"url": "https://www.langchain.com/langsmith", "text": "<PERSON><PERSON><PERSON>", "title": "", "rel": "", "position": 3, "location": "navigation", "button_type": "premium", "css_data": {"classes": ["navbar_navlink"], "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".navbar_navlink", "style": "color: var(--colors--green--green-400);\nfont-size: 1.25rem;\ntext-decoration: none"}]}, "score": 0.95, "individual_reason": "Premium button in navigation, likely a key product page."}, {"url": "https://www.langchain.com/langgraph-platform", "text": "LangGraph Platform", "title": "", "rel": "", "position": 4, "location": "navigation", "button_type": "premium", "css_data": {"classes": ["navbar_navlink"], "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".navbar_navlink", "style": "color: var(--colors--green--green-400);\nfont-size: 1.25rem;\ntext-decoration: none"}]}, "score": 0.95, "individual_reason": "Premium button in navigation, likely a key product page."}, {"url": "https://www.langchain.com/resources", "text": "Resources Hub", "title": "", "rel": "", "position": 5, "location": "navigation", "button_type": "none", "css_data": {"classes": ["navbar_navlink"], "id": "w-node-_51e31229-b145-848e-f1dc-e92c526909bf-62764940", "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".navbar_navlink", "style": "color: var(--colors--green--green-400);\nfont-size: 1.25rem;\ntext-decoration: none"}]}, "score": 0.85, "individual_reason": "Navigation link to resources hub, likely frequently accessed."}, {"url": "https://blog.langchain.com/", "text": "Blog", "title": "", "rel": "", "position": 6, "location": "navigation", "button_type": "none", "css_data": {"classes": ["navbar_navlink"], "id": "w-node-_51e31229-b145-848e-f1dc-e92c526909bd-62764940", "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".navbar_navlink", "style": "color: var(--colors--green--green-400);\nfont-size: 1.25rem;\ntext-decoration: none"}]}, "score": 0.85, "individual_reason": "Navigation link to blog, likely frequently accessed."}, {"url": "https://www.langchain.com/customers", "text": "Customer Stories", "title": "", "rel": "", "position": 7, "location": "navigation", "button_type": "none", "css_data": {"classes": ["navbar_navlink"], "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".navbar_navlink", "style": "color: var(--colors--green--green-400);\nfont-size: 1.25rem;\ntext-decoration: none"}]}, "score": 0.85, "individual_reason": "Navigation link to customer stories, likely frequently accessed."}, {"url": "https://academy.langchain.com/", "text": "LangChain Academy", "title": "", "rel": "", "position": 8, "location": "navigation", "button_type": "none", "css_data": {"classes": ["navbar_navlink"], "id": "w-node-_51e31229-b145-848e-f1dc-e92c526909c1-62764940", "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".navbar_navlink", "style": "color: var(--colors--green--green-400);\nfont-size: 1.25rem;\ntext-decoration: none"}]}, "score": 0.85, "individual_reason": "Navigation link to LangChain Academy, likely frequently accessed."}, {"url": "https://www.langchain.com/community", "text": "Community", "title": "", "rel": "", "position": 9, "location": "navigation", "button_type": "none", "css_data": {"classes": ["navbar_navlink"], "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".navbar_navlink", "style": "color: var(--colors--green--green-400);\nfont-size: 1.25rem;\ntext-decoration: none"}]}, "score": 0.85, "individual_reason": "Navigation link to community, likely frequently accessed."}, {"url": "https://www.langchain.com/experts", "text": "Experts", "title": "", "rel": "", "position": 10, "location": "navigation", "button_type": "none", "css_data": {"classes": ["navbar_navlink"], "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".navbar_navlink", "style": "color: var(--colors--green--green-400);\nfont-size: 1.25rem;\ntext-decoration: none"}]}, "score": 0.85, "individual_reason": "Navigation link to experts, likely frequently accessed."}, {"url": "https://changelog.langchain.com/", "text": "Changelog", "title": "", "rel": "", "position": 11, "location": "navigation", "button_type": "none", "css_data": {"classes": ["navbar_navlink"], "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".navbar_navlink", "style": "color: var(--colors--green--green-400);\nfont-size: 1.25rem;\ntext-decoration: none"}]}, "score": 0.85, "individual_reason": "Navigation link to changelog, likely frequently accessed."}, {"url": "https://langchain-ai.github.io/langgraph/tutorials/introduction/", "text": "LangGraph", "title": "", "rel": "", "position": 12, "location": "navigation", "button_type": "none", "css_data": {"classes": ["navbar_navlink"], "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".navbar_navlink", "style": "color: var(--colors--green--green-400);\nfont-size: 1.25rem;\ntext-decoration: none"}]}, "score": 0.85, "individual_reason": "Navigation link to LangGraph, likely frequently accessed."}, {"url": "https://docs.smith.langchain.com/", "text": "<PERSON><PERSON><PERSON>", "title": "", "rel": "", "position": 13, "location": "navigation", "button_type": "none", "css_data": {"classes": ["navbar_navlink"], "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".navbar_navlink", "style": "color: var(--colors--green--green-400);\nfont-size: 1.25rem;\ntext-decoration: none"}]}, "score": 0.85, "individual_reason": "Navigation link to LangSmith, likely frequently accessed."}, {"url": "https://python.langchain.com/docs/introduction/", "text": "<PERSON><PERSON><PERSON><PERSON>", "title": "", "rel": "", "position": 14, "location": "navigation", "button_type": "none", "css_data": {"classes": ["navbar_navlink"], "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".navbar_navlink", "style": "color: var(--colors--green--green-400);\nfont-size: 1.25rem;\ntext-decoration: none"}]}, "score": 0.85, "individual_reason": "Navigation link to LangChain, likely frequently accessed."}, {"url": "https://langchain-ai.github.io/langgraphjs/tutorials/quickstart/", "text": "LangGraph", "title": "", "rel": "", "position": 15, "location": "navigation", "button_type": "none", "css_data": {"classes": ["navbar_navlink"], "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".navbar_navlink", "style": "color: var(--colors--green--green-400);\nfont-size: 1.25rem;\ntext-decoration: none"}]}, "score": 0.85, "individual_reason": "Navigation link to LangGraph, likely frequently accessed."}, {"url": "https://js.langchain.com/docs/introduction/", "text": "<PERSON><PERSON><PERSON><PERSON>", "title": "", "rel": "", "position": 17, "location": "navigation", "button_type": "none", "css_data": {"classes": ["navbar_navlink"], "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".navbar_navlink", "style": "color: var(--colors--green--green-400);\nfont-size: 1.25rem;\ntext-decoration: none"}]}, "score": 0.85, "individual_reason": "Navigation link to LangChain, likely frequently accessed."}, {"url": "https://www.langchain.com/about", "text": "About", "title": "", "rel": "", "position": 18, "location": "navigation", "button_type": "none", "css_data": {"classes": ["navbar_navlink"], "id": "w-node-_86f16cab-d26d-6ec0-1e8b-7f5d62764992-62764940", "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".navbar_navlink", "style": "color: var(--colors--green--green-400);\nfont-size: 1.25rem;\ntext-decoration: none"}]}, "score": 0.85, "individual_reason": "Navigation link to about page, likely frequently accessed."}, {"url": "https://www.langchain.com/careers", "text": "Careers", "title": "", "rel": "", "position": 19, "location": "navigation", "button_type": "none", "css_data": {"classes": ["navbar_navlink"], "id": "w-node-_86f16cab-d26d-6ec0-1e8b-7f5d62764994-62764940", "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".navbar_navlink", "style": "color: var(--colors--green--green-400);\nfont-size: 1.25rem;\ntext-decoration: none"}]}, "score": 0.85, "individual_reason": "Navigation link to careers, likely frequently accessed."}, {"url": "https://www.langchain.com/pricing", "text": "Pricing", "title": "", "rel": "", "position": 20, "location": "navigation", "button_type": "none", "css_data": {"classes": ["navbar_link", "is-pricing", "menu"], "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".navbar_link", "style": "border-radius: 4rem;\npadding: 0.5rem 1.5rem;\nfont-size: 1.25rem;\ntext-decoration: none;\ntransition: background-color 0.2s"}]}, "score": 0.85, "individual_reason": "Navigation link to pricing, likely frequently accessed."}, {"url": "https://www.langchain.com/contact-sales", "text": "Get a demo", "title": "", "rel": "", "position": 21, "location": "navigation", "button_type": "bootstrap-btn", "css_data": {"classes": ["button", "is-nav-copy", "w-button"], "matched_external_css": [{"selector": "a", "style": ""}, {"selector": ".w-button", "style": "color: #fff;\nline-height: inherit;\ncursor: pointer;\nbackground-color: #3898ec;\nborder: 0;\nborder-radius: 0;\npadding: 9px 15px;\ntext-decoration: none;\ndisplay: inline-block"}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".button", "style": "border: 1px solid var(--colors--violet--violet-400);\nbackground-color: var(--colors--violet--violet-400);\ncolor: var(--colors--green--green-400);\ntext-align: center;\nborder-radius: 4rem;\npadding: 1rem 2.5rem;\nfont-size: 1.25rem;\nfont-weight: 500;\nline-height: 1.6;\ntext-decoration: none;\ntransition: all 0.2s"}]}, "score": 0.9, "individual_reason": "Bootstrap button in navigation, indicating a call to action."}, {"url": "https://smith.langchain.com/", "text": "Sign up", "title": "", "rel": "", "position": 22, "location": "navigation", "button_type": "bootstrap-btn", "css_data": {"classes": ["button", "is-nav", "w-button"], "matched_external_css": [{"selector": "a", "style": ""}, {"selector": ".w-button", "style": "color: #fff;\nline-height: inherit;\ncursor: pointer;\nbackground-color: #3898ec;\nborder: 0;\nborder-radius: 0;\npadding: 9px 15px;\ntext-decoration: none;\ndisplay: inline-block"}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".button", "style": "border: 1px solid var(--colors--violet--violet-400);\nbackground-color: var(--colors--violet--violet-400);\ncolor: var(--colors--green--green-400);\ntext-align: center;\nborder-radius: 4rem;\npadding: 1rem 2.5rem;\nfont-size: 1.25rem;\nfont-weight: 500;\nline-height: 1.6;\ntext-decoration: none;\ntransition: all 0.2s"}]}, "score": 0.9, "individual_reason": "Bootstrap button in navigation, indicating a call to action."}, {"url": "https://www.langchain.com/langchain-academy-live", "text": "Learn More", "title": "", "rel": "", "position": 48, "location": "header", "button_type": "bootstrap-btn", "css_data": {"classes": ["button", "is-link", "banner", "w-inline-block"], "matched_external_css": [{"selector": "a", "style": ""}, {"selector": ".w-inline-block", "style": "max-width: 100%;\ndisplay: inline-block"}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".button", "style": "border: 1px solid var(--colors--violet--violet-400);\nbackground-color: var(--colors--violet--violet-400);\ncolor: var(--colors--green--green-400);\ntext-align: center;\nborder-radius: 4rem;\npadding: 1rem 2.5rem;\nfont-size: 1.25rem;\nfont-weight: 500;\nline-height: 1.6;\ntext-decoration: none;\ntransition: all 0.2s"}]}, "score": 0.5, "individual_reason": "Fallback selection"}, {"url": "https://blog.langchain.dev/customers-replit/", "text": "", "title": "", "rel": "", "position": 49, "location": "main", "button_type": "none", "css_data": {"classes": ["use-cases-link", "w-inline-block"], "data_attributes": {"data-w-id": "09de88f4-e805-7639-0f45-35c219ebb3a7"}, "matched_external_css": [{"selector": "a", "style": ""}, {"selector": ".w-inline-block", "style": "max-width: 100%;\ndisplay: inline-block"}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}]}, "score": 0.5, "individual_reason": "Fallback selection"}, {"url": "https://blog.langchain.dev/customers-rakuten/", "text": "", "title": "", "rel": "", "position": 50, "location": "main", "button_type": "none", "css_data": {"classes": ["use-cases-link", "w-inline-block"], "data_attributes": {"data-w-id": "09de88f4-e805-7639-0f45-35c219ebb3a7"}, "matched_external_css": [{"selector": "a", "style": ""}, {"selector": ".w-inline-block", "style": "max-width: 100%;\ndisplay: inline-block"}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}]}, "score": 0.5, "individual_reason": "Fallback selection"}, {"url": "https://blog.langchain.dev/customers-klarna/", "text": "", "title": "", "rel": "", "position": 51, "location": "main", "button_type": "none", "css_data": {"classes": ["use-cases-link", "w-inline-block"], "data_attributes": {"data-w-id": "09de88f4-e805-7639-0f45-35c219ebb3a7"}, "matched_external_css": [{"selector": "a", "style": ""}, {"selector": ".w-inline-block", "style": "max-width: 100%;\ndisplay: inline-block"}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}]}, "score": 0.5, "individual_reason": "Fallback selection"}, {"url": "https://blog.langchain.dev/morningstar-intelligence-engine-puts-personalized-investment-insights-at-analysts-fingertips/", "text": "", "title": "", "rel": "", "position": 52, "location": "main", "button_type": "none", "css_data": {"classes": ["use-cases-link", "w-inline-block"], "data_attributes": {"data-w-id": "09de88f4-e805-7639-0f45-35c219ebb3a7"}, "matched_external_css": [{"selector": "a", "style": ""}, {"selector": ".w-inline-block", "style": "max-width: 100%;\ndisplay: inline-block"}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}]}, "score": 0.5, "individual_reason": "Fallback selection"}, {"url": "https://blog.langchain.dev/customers-lovable/", "text": "", "title": "", "rel": "", "position": 53, "location": "main", "button_type": "none", "css_data": {"classes": ["use-cases-link", "w-inline-block"], "data_attributes": {"data-w-id": "09de88f4-e805-7639-0f45-35c219ebb3a7"}, "matched_external_css": [{"selector": "a", "style": ""}, {"selector": ".w-inline-block", "style": "max-width: 100%;\ndisplay: inline-block"}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}]}, "score": 0.5, "individual_reason": "Fallback selection"}, {"url": "https://blog.langchain.dev/customers-chrobinson/", "text": "TransportationThis global logistics provider is saving 600 hours a day using an automated order system built on LangGraph and LangSmith", "title": "", "rel": "", "position": 57, "location": "header", "button_type": "none", "css_data": {"inline_style": "opacity:0", "classes": ["use-case_item", "is-alternate", "w-inline-block"], "data_attributes": {"data-w-id": "83e5fe5d-773f-d0cf-2bf1-73903a6b6ac8"}, "matched_external_css": [{"selector": "a", "style": ""}, {"selector": ".w-inline-block", "style": "max-width: 100%;\ndisplay: inline-block"}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".use-case_item", "style": "border-radius: 2.5rem;\nflex-direction: column;\njustify-content: space-between;\nheight: 100%;\nmin-height: 33.875rem;\npadding: 1rem 1rem 3rem;\ndisplay: flex;\nposition: relative"}]}, "score": 0.5, "individual_reason": "Fallback selection"}, {"url": "https://blog.langchain.dev/customers-trellix/", "text": "SecurityAs a leading cybersecurity firm with 40k+ customers, Trellix cut log parsing from days to minutes using LangGraph and LangSmith.", "title": "", "rel": "", "position": 58, "location": "header", "button_type": "none", "css_data": {"inline_style": "opacity:0", "classes": ["use-case_item", "is-alternate", "w-inline-block"], "data_attributes": {"data-w-id": "5a1dcbdb-6674-a5ae-f64f-db00dc4b2ed8"}, "matched_external_css": [{"selector": "a", "style": ""}, {"selector": ".w-inline-block", "style": "max-width: 100%;\ndisplay: inline-block"}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".use-case_item", "style": "border-radius: 2.5rem;\nflex-direction: column;\njustify-content: space-between;\nheight: 100%;\nmin-height: 33.875rem;\npadding: 1rem 1rem 3rem;\ndisplay: flex;\nposition: relative"}]}, "score": 0.5, "individual_reason": "Fallback selection"}, {"url": "https://www.langchain.com/agents", "text": "Agents", "title": "", "rel": "", "position": 64, "location": "footer", "button_type": "none", "css_data": {"classes": ["footer_link"], "matched_external_css": [{"selector": "a", "style": ""}, {"selector": "a", "style": "color: var(--colors--green--green-400);\ntext-decoration: underline"}, {"selector": ".footer_link", "style": "color: var(--colors--green--green-100);\npadding-top: 0.5rem;\npadding-bottom: 0.5rem;\nfont-size: 1.25rem;\ntext-decoration: none;\ntransition: all 0.2s"}]}, "score": 0.5, "individual_reason": "Fallback selection"}], "total_links": 81, "consistency_hash": "b9a2d057_bd9394a1", "confidence_score": 0.85, "reasoning": "No reasoning provided (Fallback triggered to ensure correct number of links)", "rag_context_used": "Context is now retrieved by the agent via its tool.", "source_url": "https://www.langchain.com/"}