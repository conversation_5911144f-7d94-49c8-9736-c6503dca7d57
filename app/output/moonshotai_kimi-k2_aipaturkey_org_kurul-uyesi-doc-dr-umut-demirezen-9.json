{"selected_links": [{"url": "https://aipaturkey.org/", "text": "", "title": "", "rel": "", "position": 0, "location": "header", "button_type": "none", "css_data": {"matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.98, "individual_reason": "Homepage link in header with direct access and primary navigation placement - highest priority for site discovery"}, {"url": "https://aipaturkey.org/uyelik", "text": "Üyelik", "title": "", "rel": "", "position": 32, "location": "header", "button_type": "bootstrap-btn", "css_data": {"classes": ["button", "hidden", "rounded-[50px]", "border-black", "bg-black", "text-white", "after:bg-colorOrangyRed", "hover:border-colorOrangyRed", "hover:text-white", "lg:inline-block"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}, {"selector": ".button", "style": "position: relative;\nz-index: 1;\ndisplay: inline-flex;\noverflow: hidden;\nborder-width: 1px;\npadding-left: 25px;\npadding-right: 25px;\npadding-top: 10px;\npadding-bottom: 10px;\ntext-align: center;\nfont-size: 1rem;\nline-height: 1.5rem;\nfont-weight: 700;\nline-height: 1.5;\ntransition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms"}, {"selector": ".hidden", "style": "display: none"}, {"selector": ".border-black", "style": "--tw-border-opacity: 1;\nborder-color: #1d78a1"}, {"selector": ".bg-black", "style": "background-color: #1d78a1"}, {"selector": ".text-white", "style": "--tw-text-opacity: 1"}]}, "score": 0.95, "individual_reason": "Primary CTA button with bootstrap styling, high-contrast design, and membership conversion objective"}, {"url": "https://aipaturkey.org/hakkimizda", "text": "Biz <PERSON> ?", "title": "", "rel": "", "position": 3, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.92, "individual_reason": "About Us page - semantic link text suggests institutional information exploration"}, {"url": "https://aipaturkey.org/yonetim-kurulu-yonetim-kurulumuz-1", "text": "<PERSON><PERSON><PERSON><PERSON>", "title": "", "rel": "", "position": 5, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.91, "individual_reason": "Management board page - appears in both navigation and footer, high organizational hierarchy value"}, {"url": "https://aipaturkey.org/yonetim-kurulu-genel-sekreterlik-4", "text": "<PERSON><PERSON>", "title": "", "rel": "", "position": 6, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.89, "individual_reason": "Secretariat page - governance structure link with dual placement significance"}, {"url": "https://aipaturkey.org/yonetim-kurulu-danisma-kurulumuz-3", "text": "Danışma Kurulumuz", "title": "", "rel": "", "position": 9, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.88, "individual_reason": "Advisory board - governance navigation with strong institutional relevance"}, {"url": "https://aipaturkey.org/yonetim-kurulu-denetim-kurulumuz-6", "text": "<PERSON><PERSON><PERSON>", "title": "", "rel": "", "position": 7, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.87, "individual_reason": "Audit board - important governance function with consistent positioning"}, {"url": "https://aipaturkey.org/yonetim-kurulu-disiplin-kurulumuz-5", "text": "<PERSON><PERSON><PERSON><PERSON>", "title": "", "rel": "", "position": 8, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.86, "individual_reason": "Disciplinary board - completes governance structure coverage"}, {"url": "https://aipaturkey.org/calisma-alanlari", "text": "Çalışma Alanlarımız", "title": "", "rel": "", "position": 12, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.85, "individual_reason": "Working areas page - high informational value for prospective members"}, {"url": "https://aipaturkey.org/baskan", "text": "Başkanımızdan", "title": "", "rel": "", "position": 4, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.84, "individual_reason": "President's message - leadership communication directly from authority figure"}, {"url": "https://aipaturkey.org/akademi", "text": "AIPA Akademi", "title": "", "rel": "", "position": 16, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.83, "individual_reason": "Academy page with dual menu entries - educational content emphasis"}, {"url": "https://aipaturkey.org/partnerler", "text": "Partnerlerimiz", "title": "", "rel": "", "position": 13, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.82, "individual_reason": "Partners page - relationship visibility and network exploration"}, {"url": "https://aipaturkey.org/kurumsal-uyeler", "text": "Kurumsal Üyelerimiz", "title": "", "rel": "", "position": 14, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.81, "individual_reason": "Corporate members page - membership tier insight and business relevance"}, {"url": "https://aipaturkey.org/arastirmalar", "text": "Araştırmalar", "title": "", "rel": "", "position": 26, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.8, "individual_reason": "Research page - intellectual output with academic value proposition"}, {"url": "https://aipaturkey.org/haberler", "text": "<PERSON><PERSON><PERSON>", "title": "", "rel": "", "position": 29, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.79, "individual_reason": "News section - temporal content with high refresh potential"}, {"url": "https://aipaturkey.org/iletisim", "text": "İletişim", "title": "", "rel": "", "position": 30, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.78, "individual_reason": "Contact page - conversion funnel endpoint for inquiries"}, {"url": "https://aipaturkey.org/kitaplar", "text": "Kitaplar", "title": "", "rel": "", "position": 27, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.77, "individual_reason": "Books section - knowledge products with educational value"}, {"url": "https://aipaturkey.org/belgeler", "text": "Politika Belgeleri", "title": "", "rel": "", "position": 28, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.76, "individual_reason": "Policy documents - authoritative content positioning organization as thought leader"}, {"url": "https://aipajournal.com/index.php/pub/index", "text": "AIPA Journal", "title": "", "rel": "", "position": 25, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.75, "individual_reason": "AIPA Journal - external academic platform with high domain authority"}, {"url": "https://aitomorrowsummit.com", "text": "AI [Tomorrow Summit]", "title": "", "rel": "", "position": 15, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.74, "individual_reason": "AI Tomorrow Summit - branded event with marketing priority"}, {"url": "https://aipaturkey.org/etkinlikler-tomorrow-meetings-3", "text": "Tomorrow Meetings", "title": "", "rel": "", "position": 19, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.73, "individual_reason": "Tomorrow Meetings - recurring event series with audience engagement"}, {"url": "https://aipaturkey.org/etkinlikler-tomorrow-talks-4", "text": "Tomorrow Talks", "title": "", "rel": "", "position": 20, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.72, "individual_reason": "Tomorrow Talks - thought leadership content series"}, {"url": "https://aipaturkey.org/galeri", "text": "Fotoğraf & Video Galeri", "title": "", "rel": "", "position": 22, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.71, "individual_reason": "Photo & Video Gallery - visual content engagement with multimedia appeal"}, {"url": "https://aipaturkey.org/etkinlikler-tv-ve-radyo-yayinlari-2", "text": "TV ve Ra<PERSON>o <PERSON>ı", "title": "", "rel": "", "position": 21, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.7, "individual_reason": "TV and Radio Broadcasts - media presence indicating authority and reach"}, {"url": "https://aipaturkey.org/etkinlikler-soylesiler-5", "text": "<PERSON><PERSON><PERSON><PERSON>", "title": "", "rel": "", "position": 23, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.69, "individual_reason": "Interviews page - personal stories and expert perspectives"}, {"url": "https://aipaturkey.org/uploads/f/m/MZGwE30SklXg.pdf", "text": "Tüzük", "title": "", "rel": "", "position": 10, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.68, "individual_reason": "Statute document - governance transparency and legal foundation"}, {"url": "https://aipaturkey.org/hakkimizda#core-value", "text": "Logo and Medya Kiti", "title": "", "rel": "", "position": 11, "location": "navigation", "button_type": "action-text", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.67, "individual_reason": "Logo and Media Kit - brand assets with press and promotional value"}, {"url": "https://aipaturkey.org/pbl/dil/en", "text": "EN", "title": "", "rel": "", "position": 31, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.66, "individual_reason": "English language option - international audience accessibility"}, {"url": "https://www.linkedin.com/company/aipatr/mycompany/", "text": "", "title": "Linkedin", "rel": "", "position": 38, "location": "footer", "button_type": "none", "css_data": {"classes": ["pbl-social-linkedin"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.65, "individual_reason": "LinkedIn profile - professional network engagement"}, {"url": "https://www.youtube.com/@AIPATurkey", "text": "", "title": "Youtube", "rel": "", "position": 39, "location": "footer", "button_type": "none", "css_data": {"classes": ["pbl-social-youtube"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.64, "individual_reason": "YouTube channel - video content platform with engagement potential"}], "total_links": 42, "consistency_hash": "9581493e_cd263c04", "confidence_score": 0.92, "reasoning": "No reasoning provided", "rag_context_used": "Context is now retrieved by the agent via its tool.", "source_url": "https://aipaturkey.org/kurul-uyesi-doc-dr-umut-demirezen-9"}