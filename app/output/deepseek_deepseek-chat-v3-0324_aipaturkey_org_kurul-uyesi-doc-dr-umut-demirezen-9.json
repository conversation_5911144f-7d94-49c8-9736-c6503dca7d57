{"selected_links": [{"url": "https://aipaturkey.org/", "text": "", "title": "", "rel": "", "position": 0, "location": "header", "button_type": "none", "css_data": {"matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.9, "individual_reason": "Homepage link in the header, likely to be clicked for navigation."}, {"url": "https://aipaturkey.org/hakkimizda", "text": "Biz <PERSON> ?", "title": "", "rel": "", "position": 3, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.85, "individual_reason": "About Us page in navigation, relevant for user intent."}, {"url": "https://aipaturkey.org/baskan", "text": "Başkanımızdan", "title": "", "rel": "", "position": 4, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.8, "individual_reason": "President's page, likely to be clicked for organizational info."}, {"url": "https://aipaturkey.org/yonetim-kurulu-yonetim-kurulumuz-1", "text": "<PERSON><PERSON><PERSON><PERSON>", "title": "", "rel": "", "position": 5, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.8, "individual_reason": "Management Board page, relevant for organizational structure."}, {"url": "https://aipaturkey.org/yonetim-kurulu-genel-sekreterlik-4", "text": "<PERSON><PERSON>", "title": "", "rel": "", "position": 6, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.8, "individual_reason": "General Secretariat page, relevant for organizational structure."}, {"url": "https://aipaturkey.org/yonetim-kurulu-denetim-kurulumuz-6", "text": "<PERSON><PERSON><PERSON>", "title": "", "rel": "", "position": 7, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.8, "individual_reason": "Audit Board page, relevant for organizational structure."}, {"url": "https://aipaturkey.org/yonetim-kurulu-disiplin-kurulumuz-5", "text": "<PERSON><PERSON><PERSON><PERSON>", "title": "", "rel": "", "position": 8, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.8, "individual_reason": "Disciplinary Board page, relevant for organizational structure."}, {"url": "https://aipaturkey.org/yonetim-kurulu-danisma-kurulumuz-3", "text": "Danışma Kurulumuz", "title": "", "rel": "", "position": 9, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.8, "individual_reason": "Advisory Board page, relevant for organizational structure."}, {"url": "https://aipaturkey.org/uploads/f/m/MZGwE30SklXg.pdf", "text": "Tüzük", "title": "", "rel": "", "position": 10, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.75, "individual_reason": "Statute PDF, relevant for organizational info."}, {"url": "https://aipaturkey.org/hakkimizda#core-value", "text": "Logo and Medya Kiti", "title": "", "rel": "", "position": 11, "location": "navigation", "button_type": "action-text", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.7, "individual_reason": "Logo and Media Kit, relevant for resources."}, {"url": "https://aipaturkey.org/calisma-alanlari", "text": "Çalışma Alanlarımız", "title": "", "rel": "", "position": 12, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.8, "individual_reason": "Work Areas page, relevant for user intent."}, {"url": "https://aipaturkey.org/partnerler", "text": "Partnerlerimiz", "title": "", "rel": "", "position": 13, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.8, "individual_reason": "Partners page, relevant for organizational info."}, {"url": "https://aipaturkey.org/kurumsal-uyeler", "text": "Kurumsal Üyelerimiz", "title": "", "rel": "", "position": 14, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.8, "individual_reason": "Corporate Members page, relevant for organizational info."}, {"url": "https://aitomorrowsummit.com", "text": "AI [Tomorrow Summit]", "title": "", "rel": "", "position": 15, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.75, "individual_reason": "AI Tomorrow Summit, relevant for events."}, {"url": "https://aipaturkey.org/akademi", "text": "AIPA Akademi", "title": "", "rel": "", "position": 16, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.8, "individual_reason": "AIPA Academy, relevant for educational resources."}, {"url": "https://aipaturkey.org/etkinlikler-tomorrow-meetings-3", "text": "Tomorrow Meetings", "title": "", "rel": "", "position": 19, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.75, "individual_reason": "Tomorrow Meetings, relevant for events."}, {"url": "https://aipaturkey.org/etkinlikler-tomorrow-talks-4", "text": "Tomorrow Talks", "title": "", "rel": "", "position": 20, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.75, "individual_reason": "Tomorrow Talks, relevant for events."}, {"url": "https://aipaturkey.org/etkinlikler-tv-ve-radyo-yayinlari-2", "text": "TV ve Ra<PERSON>o <PERSON>ı", "title": "", "rel": "", "position": 21, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.7, "individual_reason": "TV and Radio Broadcasts, relevant for media."}, {"url": "https://aipaturkey.org/galeri", "text": "Fotoğraf & Video Galeri", "title": "", "rel": "", "position": 22, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.7, "individual_reason": "Photo & Video Gallery, relevant for media."}, {"url": "https://aipaturkey.org/etkinlikler-soylesiler-5", "text": "<PERSON><PERSON><PERSON><PERSON>", "title": "", "rel": "", "position": 23, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.7, "individual_reason": "Interviews, relevant for events."}, {"url": "https://aipajournal.com/index.php/pub/index", "text": "AIPA Journal", "title": "", "rel": "", "position": 25, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.75, "individual_reason": "AIPA Journal, relevant for publications."}, {"url": "https://aipaturkey.org/arastirmalar", "text": "Araştırmalar", "title": "", "rel": "", "position": 26, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.8, "individual_reason": "Research page, relevant for academic resources."}, {"url": "https://aipaturkey.org/kitaplar", "text": "Kitaplar", "title": "", "rel": "", "position": 27, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.75, "individual_reason": "Books page, relevant for resources."}, {"url": "https://aipaturkey.org/belgeler", "text": "Politika Belgeleri", "title": "", "rel": "", "position": 28, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.75, "individual_reason": "Policy Documents, relevant for organizational info."}, {"url": "https://aipaturkey.org/haberler", "text": "<PERSON><PERSON><PERSON>", "title": "", "rel": "", "position": 29, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.8, "individual_reason": "News page, relevant for updates."}, {"url": "https://aipaturkey.org/iletisim", "text": "İletişim", "title": "", "rel": "", "position": 30, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item", "drop-trigger"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.85, "individual_reason": "Contact page, relevant for user intent."}, {"url": "https://aipaturkey.org/pbl/dil/en", "text": "EN", "title": "", "rel": "", "position": 31, "location": "navigation", "button_type": "none", "css_data": {"classes": ["nav-link-item"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.7, "individual_reason": "English language option, relevant for accessibility."}, {"url": "https://aipaturkey.org/uyelik", "text": "Üyelik", "title": "", "rel": "", "position": 32, "location": "header", "button_type": "bootstrap-btn", "css_data": {"classes": ["button", "hidden", "rounded-[50px]", "border-black", "bg-black", "text-white", "after:bg-colorOrangyRed", "hover:border-colorOrangyRed", "hover:text-white", "lg:inline-block"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}, {"selector": ".button", "style": "position: relative;\nz-index: 1;\ndisplay: inline-flex;\noverflow: hidden;\nborder-width: 1px;\npadding-left: 25px;\npadding-right: 25px;\npadding-top: 10px;\npadding-bottom: 10px;\ntext-align: center;\nfont-size: 1rem;\nline-height: 1.5rem;\nfont-weight: 700;\nline-height: 1.5;\ntransition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms"}, {"selector": ".hidden", "style": "display: none"}, {"selector": ".border-black", "style": "--tw-border-opacity: 1;\nborder-color: #1d78a1"}, {"selector": ".bg-black", "style": "background-color: #1d78a1"}, {"selector": ".text-white", "style": "--tw-text-opacity: 1"}]}, "score": 0.9, "individual_reason": "Membership button in header, call-to-action."}, {"url": "https://www.facebook.com/aipaturkey/", "text": "", "title": "Facebook", "rel": "", "position": 35, "location": "footer", "button_type": "none", "css_data": {"classes": ["pbl-social-facebook"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.5, "individual_reason": "Fallback selection"}, {"url": "https://twitter.com/aipaturkey", "text": "", "title": "Twitter", "rel": "", "position": 36, "location": "footer", "button_type": "none", "css_data": {"classes": ["pbl-social-twitter"], "matched_external_css": [{"selector": "a", "style": "color: inherit;\ntext-decoration: inherit"}, {"selector": "a", "style": "transition-property: all;\ntransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\ntransition-duration: 300ms;\ntransition-timing-function: linear"}]}, "score": 0.5, "individual_reason": "Fallback selection"}], "total_links": 42, "consistency_hash": "81cc1ab9_cd263c04", "confidence_score": 0.85, "reasoning": "No reasoning provided (Fallback triggered to ensure correct number of links)", "rag_context_used": "Context is now retrieved by the agent via its tool.", "source_url": "https://aipaturkey.org/kurul-uyesi-doc-dr-umut-demirezen-9"}