#!/usr/bin/env python3
"""
Main entrypoint for the Link Prefetching Judge with a Gradio interface.
Allows users to input a URL and see the prefetching recommendations.
"""

import asyncio
import json
import gradio as gr
from agent import EfficientAgentOnlyJudgeWithCSS, LinkInfo

# Initialize the agent once
try:
    judge = EfficientAgentOnlyJudgeWithCSS()
except ValueError as e:
    print(f"Error initializing agent: {e}")
    print("Please make sure your API key is set correctly in app/config.yaml")
    judge = None

async def analyze_and_format_url(url: str) -> str:
    """
    Analyzes a given URL and formats the result for Gradio output.
    """
    if not judge:
        return "Agent could not be initialized. Please check your API key and restart the application."
    if not url:
        return "Please enter a URL."

    print(f"\n[INFO] Starting analysis for URL: {url}")

    try:
        result = await judge.analyze_url(url)
    except Exception as e:
        return f"An unexpected error occurred during analysis: {e}"

    if "error" in result:
        return f"Error analyzing URL: {result['error']}"

    # Display the overall confidence score prominently
    detailed_reasoning = f"""
**Analysis for:** {url}
---
- **Overall Confidence Score:** `{result.get('confidence_score', 0.0):.2f}`
- **Total links found on page:** `{result.get('total_links', 'N/A')}`

**Agent's Overall Reasoning:**
```
{result.get('reasoning', 'N/A')}
```

**RAG System Interaction:**
```
{result.get('rag_context_used', 'Agent retrieves context as needed via its tools.')}
```
---
### Selected links for prefetching:
"""

    output_lines = [detailed_reasoning]

    selected_links = result.get('selected_links', [])
    if not selected_links:
        output_lines.append("No links were selected.")
    else:
        for i, link_data in enumerate(selected_links, 1):
            # The agent now returns dictionaries with merged data
            link_info = f"""
{i}. **URL:** [{link_data['url']}]({link_data['url']})
   - **Score:** `{link_data.get('score', 'N/A'):.2f}`
   - **Reason:** {link_data.get('individual_reason', 'N/A')}
   - **Text:** {link_data['text']}
   - **Location:** {link_data['location']}
   - **Button Type:** {link_data['button_type']}
"""
            output_lines.append(link_info)

    final_output_string = "\n".join(output_lines)

    # Print the final formatted result to the console for logging
    print("\n" + "="*50)
    print(f"ANALYSIS COMPLETE FOR: {url}")
    print("="*50)
    # We print the markdown-formatted string, which is fine for console logging.
    print(final_output_string)
    print("="*50 + "\n")


    return final_output_string

def main():
    """Launches the Gradio interface."""
    iface = gr.Interface(
        fn=analyze_and_format_url,
        inputs=gr.Textbox(
            label="URL to Analyze", 
            placeholder="e.g., https://www.google.com"
        ),
        outputs=gr.Markdown(label="Analysis Result"),
        title="Link Prefetching Judge",
        description="Enter a URL to analyze its links and get prefetching recommendations from an AI agent.",
        examples=[["https://www.github.com"], ["https://news.ycombinator.com"]],
        allow_flagging="never"
    )

    print("Starting Gradio interface...")
    # Launch on all network interfaces to be accessible from outside the Docker container
    iface.launch(server_name="0.0.0.0", server_port=7860)

if __name__ == "__main__":
    main() 