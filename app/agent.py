#!/usr/bin/env python3
"""
Agent-related classes for the Link Prefetching Judge, now powered by RAGFlow.
"""

import asyncio
import hashlib
import json
import urllib3
import warnings
import logging
import time
import os
import re
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass, field
import yaml

import requests
from bs4 import BeautifulSoup
from pydantic import BaseModel, Field
import cssutils

from agno.agent import Agent
from agno.tools import tool
from agno.tools.reasoning import ReasoningTools
from agno.models.openai import OpenAIChat
from ragflow_sdk import RAGFlow

# Suppress SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
warnings.filterwarnings("ignore")
cssutils.log.setLevel(logging.FATAL)


def load_config(config_path: str = "config.yaml") -> Dict[str, Any]:
    """Load the configuration from a YAML file."""
    try:
        with open(config_path, 'r') as stream:
            return yaml.safe_load(stream) or {}
    except (FileNotFoundError, yaml.YAMLError):
        return {}


@dataclass
class LinkInfo:
    """Information about a link found in HTML, now with CSS data."""
    url: str
    text: str
    title: str
    rel: str
    position: int = 0
    location: str = "unknown"
    button_type: str = "none"
    css_data: Dict[str, Any] = field(default_factory=dict)


class ScoredLink(BaseModel):
    """Represents a single link with its selection score and reasoning."""
    url: str = Field(description="The exact URL of the link.")
    score: float = Field(description="The likelihood score (0.0 to 1.0) of the user clicking this link next.")
    reason: str = Field(description="A brief reason for assigning this specific score.")


class LinkSelection(BaseModel):
    """Structured output for link selection with individual scores."""
    selected_links: List[ScoredLink] = Field(
        description="List of selected link objects, each with a URL, score, and reason."
    )
    overall_reasoning: str = Field(
        description="Detailed overall reasoning for why this set of links was selected.",
        alias="reasoning"
    )
    consistency_hash: str = Field(
        description="Hash to ensure consistency across runs"
    )
    confidence_score: float = Field(
        description="Overall confidence score for the entire selection (0.0 to 1.0)"
    )


class EfficientAgentOnlyJudgeWithCSS:
    """Efficient agent-only link prefetching judge, enhanced with RAGFlow."""
    
    def __init__(self, model_name: Optional[str] = None):
        """Initialize the agent, models, and RAGFlow client."""
        config = load_config()

        openrouter_config = config.get("openrouter", {})
        or_api_key = openrouter_config.get("api_key")
        or_base_url = openrouter_config.get("base_url")
        or_model_name = openrouter_config.get("model")

        if not or_api_key or not or_base_url or not or_model_name:
            raise ValueError("OpenRouter configuration (api_key, base_url, model) not found in config.yaml.")
        
        self.num_links_to_select = config.get("number_of_links", 3)
        
        ragflow_api_key = config.get("ragflow_api_key")
        ragflow_base_url = config.get("ragflow_base_url")
        self.ragflow_dataset_name = config.get("ragflow_dataset_name")

        if not ragflow_api_key or ragflow_api_key == "YOUR_RAGFLOW_API_KEY_HERE":
            raise ValueError("RAGFlow API key not found or not configured in config.yaml.")
        
        self.model = OpenAIChat(
            id=or_model_name,
            api_key=or_api_key,
            base_url=or_base_url
        )
        
        # Initialize RAGFlow client and dataset
        try:
            self.ragflow_client = RAGFlow(ragflow_api_key, ragflow_base_url)
            datasets = self.ragflow_client.list_datasets(name=self.ragflow_dataset_name)
            if not datasets:
                # The dataset does not exist. We raise a critical error asking the user to create it.
                # This is more robust than trying to guess the creation parameters.
                raise RuntimeError(
                    f"CRITICAL: Dataset '{self.ragflow_dataset_name}' not found in RAGFlow. "
                    "Please create it manually in the RAGFlow UI and ensure you select a "
                    "valid embedding model (e.g., 'bge-base-en-v1.5') to enable semantic search."
                )
            else:
                self.dataset = datasets[0]
        except Exception as e:
            raise RuntimeError(f"Failed to initialize RAGFlow client or find the dataset: {e}")

        # Enhanced RAG retrieval function with better prompting capabilities
        @tool(show_result=True)
        async def retrieve_rag_context(question: str) -> str:
            """
            Retrieves relevant context from the RAG system for a given question about the webpage content.
            Use this tool to understand the page's purpose, main topics, and likely user actions.
            Always use this tool before making link selection decisions.

            Args:
                question: A specific question about link prefetching principles, user behavior, or webpage analysis

            Returns:
                Relevant context from the knowledge base to inform link selection decisions
            """
            print(f"[INFO] RAG Tool invoked with question: '{question}'")
            try:
                # This performs a semantic search by default.
                retrieved_chunks = self.ragflow_client.retrieve(
                    question=question,
                    dataset_ids=[self.dataset.id],
                    top_k=5
                )
                if not retrieved_chunks:
                    return "No relevant context found. Consider asking a more specific question about link prefetching principles."

                print("[INFO] Semantic search successful.")
                context = "\n".join([chunk.content for chunk in retrieved_chunks])
                return f"Retrieved context for '{question}':\n\n{context}"

            except Exception as e:
                error_message = f"CRITICAL RAG system Error: {e}. The dataset '{self.ragflow_dataset_name}' may not be configured correctly for semantic search. Please check the RAGFlow service."
                print(f"[ERROR] {error_message}")
                return error_message

        # Tool for generating optimal RAG queries
        @tool(show_result=True)
        async def generate_rag_query(analysis_context: str, specific_need: str) -> str:
            """
            Generates optimized questions for querying the RAG knowledge base.
            Use this tool to formulate the best possible questions for retrieving relevant information.

            Args:
                analysis_context: Current context of your analysis (e.g., "analyzing 15 links on an e-commerce page")
                specific_need: What specific information you need (e.g., "criteria for prioritizing product links")

            Returns:
                An optimized question for the RAG system
            """
            print(f"[INFO] RAG Query Generator invoked")

            # Generate contextually relevant questions based on the analysis needs
            query_templates = {
                "general_principles": f"What are the fundamental principles for selecting links for prefetching in the context of {analysis_context}?",
                "user_behavior": f"How do user behavior patterns influence link selection for prefetching when {analysis_context}?",
                "performance_optimization": f"What performance considerations should guide link prefetching decisions for {analysis_context}?",
                "button_prioritization": f"How should different button types and call-to-action elements be prioritized for prefetching when {analysis_context}?",
                "navigation_analysis": f"What role should navigation links play in prefetching decisions for {analysis_context}?",
                "content_relevance": f"How should content relevance and user intent be evaluated for link prefetching when {analysis_context}?"
            }

            # Select the most appropriate query based on the specific need
            if "criteria" in specific_need.lower() or "principle" in specific_need.lower():
                optimal_query = query_templates["general_principles"]
            elif "user" in specific_need.lower() or "behavior" in specific_need.lower():
                optimal_query = query_templates["user_behavior"]
            elif "performance" in specific_need.lower() or "speed" in specific_need.lower():
                optimal_query = query_templates["performance_optimization"]
            elif "button" in specific_need.lower() or "cta" in specific_need.lower():
                optimal_query = query_templates["button_prioritization"]
            elif "navigation" in specific_need.lower() or "menu" in specific_need.lower():
                optimal_query = query_templates["navigation_analysis"]
            elif "content" in specific_need.lower() or "relevance" in specific_need.lower():
                optimal_query = query_templates["content_relevance"]
            else:
                # Default to general principles with specific context
                optimal_query = f"What are the best practices for {specific_need} when {analysis_context}?"

            return f"Generated optimal RAG query: '{optimal_query}'\n\nThis query is specifically designed to retrieve the most relevant information for your current analysis needs."

        # Enhanced link analysis tool
        @tool(show_result=True)
        async def analyze_link_patterns(links_data: str) -> str:
            """
            Analyzes the provided links data to identify patterns and characteristics.
            Use this tool to understand the structure and types of links on the page.

            Args:
                links_data: Formatted string containing link information

            Returns:
                Analysis of link patterns, types, and characteristics
            """
            print(f"[INFO] Link Analysis Tool invoked")
            try:
                # Parse the links data and provide analysis
                lines = links_data.split('\n')
                link_count = len([line for line in lines if line.strip().startswith('URL:')])

                # Analyze button types
                button_types = []
                locations = []
                for line in lines:
                    if 'Button Type:' in line:
                        button_type = line.split('Button Type:')[1].strip()
                        button_types.append(button_type)
                    elif 'Location:' in line:
                        location = line.split('Location:')[1].strip()
                        locations.append(location)

                analysis = f"""Link Pattern Analysis:
- Total links found: {link_count}
- Button types distribution: {dict(zip(*[list(set(button_types)), [button_types.count(x) for x in set(button_types)]]))}
- Location distribution: {dict(zip(*[list(set(locations)), [locations.count(x) for x in set(locations)]]))}
- Primary action buttons: {len([bt for bt in button_types if bt in ['call-to-action', 'primary', 'signup', 'login']])}
- Navigation links: {len([loc for loc in locations if loc == 'navigation'])}
- Content links: {len([loc for loc in locations if loc in ['main', 'content']])}

Recommendations:
- Prioritize call-to-action and primary buttons
- Consider navigation links for user flow
- Analyze content links for relevance"""

                return analysis

            except Exception as e:
                return f"Error analyzing link patterns: {str(e)}"

        self.retrieve_rag_context_tool = retrieve_rag_context
        self.analyze_link_patterns_tool = analyze_link_patterns
        self.generate_rag_query_tool = generate_rag_query

        # Create reasoning tools for enhanced thinking capabilities
        reasoning_tools = ReasoningTools(
            think=True,
            analyze=True,
            add_instructions=True
        )

        self.agent = Agent(
            model=self.model,
            name="RAG-Enhanced Link Prefetching Judge with Advanced Reasoning",
            description=f"An intelligent agent that uses systematic reasoning and RAG knowledge to select exactly {self.num_links_to_select} links for prefetching with 100% tool utilization.",
            goal=f"To consistently select the {self.num_links_to_select} most important links for prefetching by systematically using all available tools for analysis and decision-making.",
            instructions=[
                "🎯 CORE MISSION: You are an expert link prefetching judge that MUST use tools for every decision.",
                "",
                "📋 MANDATORY TOOL USAGE WORKFLOW (100% TOOL UTILIZATION):",
                "1. ALWAYS start by using `think` tool to plan your analysis approach",
                "2. ALWAYS use `generate_rag_query` tool to create optimal questions for the knowledge base",
                "3. ALWAYS use `retrieve_rag_context` tool with the generated query to get prefetching principles",
                "4. ALWAYS use `analyze_link_patterns` tool to understand the link structure",
                "5. ALWAYS use `generate_rag_query` tool again for specific guidance on prioritization",
                "6. ALWAYS use `retrieve_rag_context` tool again with the second generated query",
                "7. ALWAYS use `analyze` tool to evaluate your findings before making final decisions",
                "",
                "🔍 SYSTEMATIC ANALYSIS PROCESS:",
                f"- You must select exactly {self.num_links_to_select} links that users are most likely to click next",
                "- Base ALL decisions on retrieved knowledge base principles",
                "- Consider link properties: purpose, location, button type, CSS styling, user intent",
                "- Prioritize call-to-action buttons, primary actions, and navigation elements",
                "- Ensure consistency - same input should yield same results",
                "",
                "🚫 CRITICAL CONSTRAINTS:",
                "- NEVER make decisions without using ALL 7 tools in sequence",
                "- NEVER select the current page or its variations",
                "- NEVER skip any step in the systematic tool-based analysis process",
                "- NEVER provide answers without referencing specific tool outputs",
                "- NEVER use generic reasoning - always base decisions on retrieved knowledge",
                "",
                "📊 EVALUATION CRITERIA (from knowledge base):",
                "1. User interaction patterns and click likelihood",
                "2. Content relevance and contextual importance",
                "3. Visual prominence and accessibility",
                "4. Performance impact and loading priority",
                "5. Link location and structural significance",
                "6. Button styling and call-to-action strength",
                "",
                "✅ OUTPUT REQUIREMENTS:",
                f"- Exactly {self.num_links_to_select} unique links with scores (0.0-1.0) and reasoning",
                "- Detailed overall reasoning referencing tool outputs",
                "- High confidence score based on systematic analysis",
                "- Evidence of tool usage in decision-making process"
            ],
            expected_output=f"A structured JSON response with exactly {self.num_links_to_select} selected links, each with URL, score, and reason based on tool analysis, plus overall reasoning and confidence score.",
            response_model=LinkSelection,
            use_json_mode=True,
            structured_outputs=True,
            tools=[reasoning_tools, retrieve_rag_context, self.analyze_link_patterns_tool, self.generate_rag_query_tool],
            show_tool_calls=True,
            debug_mode=True,
            reasoning=True,  # Enable advanced reasoning capabilities
            tool_choice="auto"  # Ensure tools are actively used
        )

    def _fetch_external_css(self, soup, base_url):
        """Fetch all external CSS files referenced in the HTML."""
        css_texts = []
        for link in soup.find_all('link', rel='stylesheet'):
            href = link.get('href')
            if not href:
                continue
            css_url = urljoin(base_url, href)
            try:
                resp = requests.get(css_url, timeout=5)
                if resp.status_code == 200:
                    css_texts.append(resp.text)
            except Exception:
                continue
        return css_texts

    def _parse_css_rules(self, css_texts):
        """Parse CSS rules from a list of CSS text blocks using cssutils."""
        rules = []
        for css in css_texts:
            try:
                sheet = cssutils.parseString(css)
                for rule in sheet:
                    if rule.type == rule.STYLE_RULE:
                        rules.append(rule)
            except Exception:
                continue
        return rules

    def _match_css_rules_to_element(self, element, rules):
        """Return a list of CSS rules that match the element by tag, class, or id."""
        matched = []
        tag = element.name
        classes = set(element.get('class', []))
        el_id = element.get('id', None)
        for rule in rules:
            selectors = rule.selectorText.split(',')
            for selector in selectors:
                selector = selector.strip()
                # Match by tag
                if selector == tag:
                    matched.append(rule)
                # Match by class
                elif selector.startswith('.') and any(cls for cls in classes if selector[1:] == cls):
                    matched.append(rule)
                # Match by id
                elif selector.startswith('#') and el_id and selector[1:] == el_id:
                    matched.append(rule)
                # Match by tag.class
                elif '.' in selector and not selector.startswith('.') and not selector.startswith('#'):
                    parts = selector.split('.')
                    if parts[0] == tag and any(cls for cls in classes if cls in parts[1:]):
                        matched.append(rule)
        return matched

    def extract_links_from_html(self, html_content: str, base_url: str = "") -> List[LinkInfo]:
        """Extract all links from HTML content, including CSS data and matched external CSS rules."""
        soup = BeautifulSoup(html_content, 'html.parser')
        # Fetch and parse external CSS
        css_texts = self._fetch_external_css(soup, base_url)
        css_rules = self._parse_css_rules(css_texts)
        links = []
        for position, link in enumerate(soup.find_all('a', href=True)):
            href = link.get('href', '').strip()
            if not href or href.startswith(('#', 'javascript:', 'mailto:', 'tel:')):
                continue
            if base_url and not href.startswith(('http://', 'https://')):
                href = urljoin(base_url, href)
            if self._is_current_page(href, base_url):
                continue
            text = link.get_text(strip=True)
            title = link.get('title', '')
            rel = link.get('rel', [])
            if isinstance(rel, list):
                rel = ' '.join(rel)
            location = self._detect_link_location(link)
            button_type = self._detect_button_type(link)
            css_data = self._extract_css_data(link)
            # Add matched external CSS rules
            matched_rules = self._match_css_rules_to_element(link, css_rules)
            if matched_rules:
                css_data['matched_external_css'] = [
                    {'selector': rule.selectorText, 'style': rule.style.cssText} for rule in matched_rules
                ]
            links.append(LinkInfo(
                url=href,
                text=text,
                title=title,
                rel=rel,
                position=position,
                location=location,
                button_type=button_type,
                css_data=css_data
            ))
        return links

    def _extract_css_data(self, link_element) -> Dict[str, Any]:
        """Extract CSS data from the link element: inline styles, class names, id, and style attributes."""
        css_data = {}
        # Inline style
        style = link_element.get('style', '')
        if style:
            css_data['inline_style'] = style
        # Class names
        classes = link_element.get('class', [])
        if classes:
            css_data['classes'] = classes
        # ID
        element_id = link_element.get('id', '')
        if element_id:
            css_data['id'] = element_id
        # Data attributes
        data_attrs = {k: v for k, v in link_element.attrs.items() if k.startswith('data-')}
        if data_attrs:
            css_data['data_attributes'] = data_attrs
        # Role
        role = link_element.get('role', '')
        if role:
            css_data['role'] = role
        # Aria attributes
        aria_attrs = {k: v for k, v in link_element.attrs.items() if k.startswith('aria-')}
        if aria_attrs:
            css_data['aria_attributes'] = aria_attrs
        return css_data

    def _detect_link_location(self, link_element) -> str:
        parent = link_element.parent
        while parent:
            if parent.name in ['nav', 'navigation']:
                return "navigation"
            elif parent.name == 'header':
                return "header"
            elif parent.name == 'footer':
                return "footer"
            elif parent.name == 'main':
                return "main"
            elif parent.name == 'aside':
                return "sidebar"
            elif parent.name == 'section':
                section_class = parent.get('class', [])
                if isinstance(section_class, list):
                    section_class = ' '.join(section_class).lower()
                else:
                    section_class = str(section_class).lower()
                if any(word in section_class for word in ['nav', 'menu', 'navigation']):
                    return "navigation"
                elif any(word in section_class for word in ['footer', 'bottom']):
                    return "footer"
                elif any(word in section_class for word in ['sidebar', 'aside']):
                    return "sidebar"
                else:
                    return "content"
            if parent.get('class'):
                class_list = parent.get('class')
                if isinstance(class_list, list):
                    class_str = ' '.join(class_list).lower()
                else:
                    class_str = str(class_list).lower()
                if any(word in class_str for word in ['nav', 'navigation', 'menu', 'navbar']):
                    return "navigation"
                elif any(word in class_str for word in ['header', 'top']):
                    return "header"
                elif any(word in class_str for word in ['footer', 'bottom']):
                    return "footer"
                elif any(word in class_str for word in ['sidebar', 'aside', 'side']):
                    return "sidebar"
                elif any(word in class_str for word in ['main', 'content', 'body']):
                    return "main"
            parent = parent.parent
        return "content"

    def _detect_button_type(self, link_element) -> str:
        classes = set()
        if link_element.get('class'):
            link_classes = link_element.get('class')
            if isinstance(link_classes, list):
                classes.update(link_classes)
            else:
                classes.add(str(link_classes))
        parent = link_element.parent
        level = 0
        while parent and level < 3:
            if parent.get('class'):
                parent_classes = parent.get('class')
                if isinstance(parent_classes, list):
                    classes.update(parent_classes)
                else:
                    classes.add(str(parent_classes))
            parent = parent.parent
            level += 1
        class_str = ' '.join(classes).lower()
        if link_element.name == 'button':
            return "button"
        button_patterns = {
            'call-to-action': ['cta', 'call-to-action', 'calltoaction', 'action-button', 'action-btn'],
            'primary': ['btn-primary', 'primary', 'primary-btn', 'primary-button', 'btn-primary'],
            'secondary': ['btn-secondary', 'secondary', 'secondary-btn', 'secondary-button'],
            'success': ['btn-success', 'success', 'success-btn', 'success-button', 'btn-success'],
            'danger': ['btn-danger', 'danger', 'danger-btn', 'danger-button', 'btn-danger'],
            'warning': ['btn-warning', 'warning', 'warning-btn', 'warning-button', 'btn-warning'],
            'info': ['btn-info', 'info', 'info-btn', 'info-button', 'btn-info'],
            'light': ['btn-light', 'light', 'light-btn', 'light-button', 'btn-light'],
            'dark': ['btn-dark', 'dark', 'dark-btn', 'dark-button', 'btn-dark'],
            'bootstrap-btn': ['btn', 'button', 'btn-default', 'btn-lg', 'btn-sm', 'btn-xs'],
            'md-button': ['md-button', 'mat-button', 'mat-raised-button', 'mat-fab', 'mat-mini-fab'],
            'foundation-btn': ['button', 'expanded', 'hollow', 'clear'],
            'bulma-btn': ['button', 'is-primary', 'is-secondary', 'is-success', 'is-danger', 'is-warning', 'is-info'],
            'tailwind-btn': ['bg-blue', 'bg-red', 'bg-green', 'bg-yellow', 'bg-gray', 'bg-black', 'bg-white'],
            'signup': ['signup', 'sign-up', 'register', 'registration', 'join', 'subscribe'],
            'login': ['login', 'signin', 'sign-in', 'log-in'],
            'download': ['download', 'dl', 'get', 'install'],
            'buy': ['buy', 'purchase', 'order', 'checkout', 'cart', 'add-to-cart'],
            'learn': ['learn', 'learn-more', 'read-more', 'explore', 'discover'],
            'contact': ['contact', 'get-in-touch', 'reach-out', 'support'],
            'demo': ['demo', 'try', 'test', 'play', 'launch'],
            'free': ['free', 'trial', 'freemium'],
            'premium': ['premium', 'pro', 'enterprise', 'business']
        }
        for button_type, patterns in button_patterns.items():
            if any(pattern in class_str for pattern in patterns):
                return button_type
        role = link_element.get('role', '').lower()
        if role in ['button', 'menuitem', 'tab']:
            return f"role-{role}"
        aria_label = link_element.get('aria-label', '').lower()
        if aria_label:
            for button_type, patterns in button_patterns.items():
                if any(pattern in aria_label for pattern in patterns):
                    return button_type
        for attr, value in link_element.attrs.items():
            if attr.startswith('data-') and isinstance(value, str):
                value_lower = value.lower()
                for button_type, patterns in button_patterns.items():
                    if any(pattern in value_lower for pattern in patterns):
                        return button_type
        style = link_element.get('style', '').lower()
        if any(prop in style for prop in ['background', 'border', 'padding', 'border-radius', 'display: inline-block']):
            return "styled-link"
        text = link_element.get_text(strip=True).lower()
        button_texts = ['click', 'submit', 'send', 'go', 'next', 'previous', 'back', 'forward', 'continue', 'start', 'begin']
        if any(btn_text in text for btn_text in button_texts):
            return "action-text"
        return "none"

    def _is_current_page(self, href: str, base_url: str) -> bool:
        if not base_url:
            return False
        href_clean = href.rstrip('/')
        base_clean = base_url.rstrip('/')
        if href_clean == base_clean:
            return True
        current_page_patterns = [
            base_clean,
            base_clean + '/',
            base_clean + '/index',
            base_clean + '/index.html',
            base_clean + '/index.php',
            base_clean + '/default',
            base_clean + '/default.html',
            base_clean + '/default.php'
        ]
        return href_clean in current_page_patterns

    def calculate_consistency_hash(self, html_content: str, links: List[LinkInfo]) -> str:
        content_hash = hashlib.md5(html_content.encode()).hexdigest()
        sorted_links = sorted(links, key=lambda x: x.url)
        links_str = json.dumps([{
            'url': link.url,
            'text': link.text,
            'title': link.title,
            'rel': link.rel,
            'position': link.position,
            'location': link.location,
            'button_type': link.button_type,
            'css_data': link.css_data
        } for link in sorted_links], sort_keys=True)
        links_hash = hashlib.md5(links_str.encode()).hexdigest()
        return f"{content_hash[:8]}_{links_hash[:8]}"

    def _format_links_for_agent(self, links: List[LinkInfo]) -> str:
        """Format links for the agent prompt, including CSS data."""
        formatted_links = []
        for i, link in enumerate(links, 1):
            css_data_str = json.dumps(link.css_data, ensure_ascii=False)
            formatted_links.append(
                f"{i}. URL: {link.url}\n"
                f"   Text: {link.text}\n"
                f"   Title: {link.title}\n"
                f"   Position: {link.position}\n"
                f"   Location: {link.location}\n"
                f"   Button Type: {link.button_type}\n"
                f"   Rel: {link.rel}\n"
                f"   CSS Data: {css_data_str}\n"
            )
        return "\n".join(formatted_links)

    async def select_prefetch_links(self, html_content: str, base_url: str = "") -> Dict[str, Any]:
        links = self.extract_links_from_html(html_content, base_url)
        if len(links) == 0:
            return {
                "selected_links": [],
                "reasoning": "No valid links found in the HTML content",
                "consistency_hash": self.calculate_consistency_hash(html_content, links),
                "confidence_score": 0.0
            }
        if len(links) < self.num_links_to_select:
            return {
                "selected_links": [],
                "total_links": len(links),
                "consistency_hash": self.calculate_consistency_hash(html_content, links),
                "confidence_score": 0.0,
                "reasoning": f"Not enough links found ({len(links)} < {self.num_links_to_select})"
            }
        

        consistency_hash = self.calculate_consistency_hash(html_content, links)
        formatted_links = self._format_links_for_agent(links)
        
        # Enhanced prompt that enforces 100% tool usage with systematic approach
        prompt = f"""🎯 LINK PREFETCHING ANALYSIS MISSION for page: '{base_url}'

You are an expert link prefetching judge with access to powerful analysis tools. Your mission is to select exactly {self.num_links_to_select} links using a systematic, tool-driven approach.

📋 AVAILABLE LINKS DATA:
{formatted_links}

🔧 MANDATORY TOOL-DRIVEN WORKFLOW:
You MUST follow this exact sequence and use ALL tools:

1. 🧠 START WITH THINKING: Use `think` tool to plan your analysis strategy
   - Input: "How should I systematically analyze these {len(links)} links to select the best {self.num_links_to_select} for prefetching?"

2. 🎯 GENERATE OPTIMAL RAG QUERY: Use `generate_rag_query` tool to create the best question
   - analysis_context: "analyzing {len(links)} links on webpage {base_url}"
   - specific_need: "criteria for selecting the most clickable links for prefetching"

3. 📚 CONSULT KNOWLEDGE BASE: Use `retrieve_rag_context` tool with the generated query
   - Use the exact question generated by the previous tool

4. 🔍 ANALYZE LINK PATTERNS: Use `analyze_link_patterns` tool to understand the structure
   - Input: The complete formatted links data provided above

5. 🎯 GENERATE SPECIFIC RAG QUERY: Use `generate_rag_query` tool for targeted guidance
   - analysis_context: "evaluating button types and link locations on {base_url}"
   - specific_need: "prioritizing call-to-action buttons versus navigation links"

6. 📚 GET SPECIFIC GUIDANCE: Use `retrieve_rag_context` tool with the second generated query
   - Use the exact question from step 5

7. 📊 FINAL ANALYSIS: Use `analyze` tool to evaluate your findings and make the final selection
   - Input: Your comprehensive analysis combining all tool outputs to select {self.num_links_to_select} links

⚠️ CRITICAL REQUIREMENTS:
- You CANNOT make any decisions without using ALL 7 tools in the exact sequence above
- Each tool output must inform your final selection
- Reference specific tool outputs in your reasoning
- The agent must generate its own RAG queries using the generate_rag_query tool
- Select EXACTLY {self.num_links_to_select} UNIQUE URLs
- NO current page variations
- Provide scores 0.0-1.0 based on comprehensive tool analysis

🎯 FINAL OUTPUT FORMAT:
After completing ALL tool usage, provide this exact JSON structure:
{{
  "selected_links": [
    {{
      "url": "exact_url_from_links_above",
      "score": 0.95,
      "reason": "Specific reason based on tool analysis and retrieved principles"
    }}
  ],
  "reasoning": "Detailed reasoning that references outputs from ALL 7 tools: think, generate_rag_query, retrieve_rag_context, analyze_link_patterns, generate_rag_query (2nd), retrieve_rag_context (2nd), and analyze tools",
  "consistency_hash": "{consistency_hash}",
  "confidence_score": 0.90
}}

🚀 BEGIN YOUR SYSTEMATIC TOOL-DRIVEN ANALYSIS NOW!"""
        try:
            # DETAILED DEBUGGING: Increase timeout to 10 minutes and add detailed logging
            print(f"[DEBUG] Sending prompt to OpenRouter API. Prompt size: {len(prompt)} characters")
            print(f"[DEBUG] Estimated tokens: ~{len(prompt)//4}")
            print(f"[DEBUG] Number of links in prompt: {len(links)}")
            
            start_time = time.time()
            response = await asyncio.wait_for(self.agent.arun(prompt), timeout=600.0)
            end_time = time.time()
            
            print(f"[DEBUG] OpenRouter API responded in {end_time - start_time:.2f} seconds")
            print(f"[DEBUG] Response type: {type(response)}")
            print(f"[DEBUG] Response attributes: {dir(response)}")
            if hasattr(response, 'content'):
                print(f"[DEBUG] Response content type: {type(response.content)}")
                print(f"[DEBUG] Response content preview: {str(response.content)[:200]}...")
            
            # SIMPLIFIED JSON PARSING: OpenRouter forced to return JSON
            raw_content = {}
            if hasattr(response, 'content'):
                if isinstance(response.content, dict):
                    raw_content = response.content
                    print(f"[DEBUG] Response already a dict")
                elif hasattr(response.content, 'model_dump'):
                    raw_content = response.content.model_dump()
                    print(f"[DEBUG] Response converted via model_dump")
                else:
                    try:
                        content_str = str(response.content).strip()
                        print(f"[DEBUG] Parsing JSON response (length: {len(content_str)})")
                        print(f"[DEBUG] First 200 chars: {content_str[:200]}")
                        
                        # Try direct JSON parsing first
                        raw_content = json.loads(content_str)
                        print(f"[DEBUG] ✅ JSON parsed successfully on first try!")
                        
                    except json.JSONDecodeError as e:
                        print(f"[DEBUG] Direct JSON parsing failed: {e}")
                        print(f"[DEBUG] Attempting JSON extraction...")
                        
                        # ROBUST JSON EXTRACTION: Handle Gemini's extra text
                        success = False
                        
                        # Method 1: Find and extract JSON block
                        try:
                            start_idx = content_str.find('{')
                            if start_idx != -1:
                                # Find matching closing brace
                                brace_count = 0
                                end_idx = start_idx
                                for i, char in enumerate(content_str[start_idx:], start_idx):
                                    if char == '{':
                                        brace_count += 1
                                    elif char == '}':
                                        brace_count -= 1
                                        if brace_count == 0:
                                            end_idx = i + 1
                                            break
                                
                                json_str = content_str[start_idx:end_idx]
                                raw_content = json.loads(json_str)
                                print(f"[DEBUG] ✅ JSON extracted successfully (Method 1)")
                                success = True
                        except Exception as e2:
                            print(f"[DEBUG] Method 1 failed: {e2}")
                        
                        # Method 2: Look for JSON in code blocks
                        if not success:
                            try:
                                # Sometimes Gemini wraps JSON in ```json ... ```
                                import re
                                json_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
                                match = re.search(json_pattern, content_str, re.DOTALL)
                                if match:
                                    json_str = match.group(1)
                                    raw_content = json.loads(json_str)
                                    print(f"[DEBUG] ✅ JSON extracted from code block (Method 2)")
                                    success = True
                            except Exception as e3:
                                print(f"[DEBUG] Method 2 failed: {e3}")
                        
                        # Method 3: Line by line search
                        if not success:
                            try:
                                lines = content_str.split('\n')
                                for line in lines:
                                    line = line.strip()
                                    if line.startswith('{') and line.endswith('}'):
                                        try:
                                            raw_content = json.loads(line)
                                            print(f"[DEBUG] ✅ JSON found in single line (Method 3)")
                                            success = True
                                            break
                                        except:
                                            continue
                            except Exception as e4:
                                print(f"[DEBUG] Method 3 failed: {e4}")
                        
                        if not success:
                            print(f"[ERROR] ❌ All JSON extraction methods failed!")
                            print(f"[DEBUG] Full response content:")
                            print(content_str)
                            raise Exception(f"Failed to extract valid JSON from OpenRouter response: {str(e)}")
            else:
                raise Exception("No content in OpenRouter response")

            scored_links_raw = raw_content.get("selected_links", [])
            reasoning = raw_content.get("reasoning", "No reasoning provided")
            confidence_score = float(raw_content.get("confidence_score", 0.5))

            selected_links_merged = []
            seen_urls = set()

            for scored_link_data in scored_links_raw:
                url = scored_link_data.get("url")
                if not url or url in seen_urls:
                    continue

                original_link = next((link for link in links if link.url == url), None)
                if original_link:
                    # Merge data from original_link and scored_link_data
                    merged_data = original_link.__dict__.copy()
                    merged_data['score'] = scored_link_data.get('score', 0.0)
                    merged_data['individual_reason'] = scored_link_data.get('reason', 'N/A')
                    selected_links_merged.append(merged_data)
                    seen_urls.add(url)

            # Fallback mechanism if the agent doesn't return the correct number of links
            if len(selected_links_merged) != self.num_links_to_select and len(links) >= self.num_links_to_select:
                reasoning += " (Fallback triggered to ensure correct number of links)"
                # Add more links if needed
                if len(selected_links_merged) < self.num_links_to_select:
                    for link in links:
                        if link.url not in seen_urls and len(selected_links_merged) < self.num_links_to_select:
                            merged_data = link.__dict__.copy()
                            merged_data['score'] = 0.5  # Assign a neutral fallback score
                            merged_data['individual_reason'] = 'Fallback selection'
                            selected_links_merged.append(merged_data)
                            seen_urls.add(link.url)
                # Trim if too many links were returned
                else:
                    selected_links_merged = selected_links_merged[:self.num_links_to_select]


            return {
                "selected_links": selected_links_merged,
                "total_links": len(links),
                "consistency_hash": consistency_hash,
                "confidence_score": confidence_score,
                "reasoning": reasoning,
                "rag_context_used": "Context is now retrieved by the agent via its tool."
            }
        except asyncio.TimeoutError:
            timeout_error_message = (
                f"TIMEOUT ERROR: Agent took longer than 10 minutes (600 seconds) to analyze the page. "
                f"This typically happens when:\n"
                f"1. The prompt is TOO LARGE for OpenRouter API ({len(links)} links found)\n"
                f"2. OpenRouter API is experiencing issues or rate limiting\n"
                f"3. The model {self.model.id} is overloaded\n\n"
                f"DEBUG INFO:\n"
                f"- Page URL: {base_url}\n"
                f"- Total links found: {len(links)}\n"
                f"- Prompt size: {len(prompt) if 'prompt' in locals() else 'unknown'} characters\n"
                f"- Estimated tokens: ~{len(prompt)//4 if 'prompt' in locals() else 'unknown'}\n\n"
                f"IMMEDIATE ACTIONS:\n"
                f"- Check OpenRouter status: https://status.openrouter.ai/\n"
                f"- Try a simpler page with fewer links\n"
                f"- Consider pre-filtering links before sending to API"
            )
            return {
                "error": timeout_error_message,
                "total_links": len(links),
                "consistency_hash": consistency_hash,
                "selected_links": [],
                "reasoning": "Analysis failed due to timeout - no results available",
                "confidence_score": 0.0,
                "rag_context_used": "N/A"
            }
        except Exception as e:
            detailed_error_message = (
                f"ANALYSIS ERROR: An unexpected error occurred during page analysis.\n\n"
                f"Error Details:\n"
                f"Type: {type(e).__name__}\n"
                f"Message: {str(e)}\n\n"
                f"Page Information:\n"
                f"- Total links found: {len(links)}\n"
                f"- Page URL: {base_url}\n\n"
                f"Possible Causes:\n"
                f"1. RAGFlow connection issues\n"
                f"2. OpenRouter API errors\n"
                f"3. Invalid page content or structure\n"
                f"4. Memory or processing limits exceeded\n"
                f"5. Network connectivity problems\n\n"
                f"Troubleshooting:\n"
                f"- Check Docker container logs: docker logs ragflow-server\n"
                f"- Verify OpenRouter API key and credits\n"
                f"- Try with a different URL\n"
                f"- Check RAGFlow dataset status"
            )
            return {
                "error": detailed_error_message,
                "total_links": len(links),
                "consistency_hash": consistency_hash,
                "selected_links": [],
                "reasoning": f"Analysis failed with error: {type(e).__name__} - {str(e)}",
                "confidence_score": 0.0,
                "rag_context_used": "N/A"
            }

    def _clean_url_for_filename(self, url: str) -> str:
        """Cleans a URL to be used as a valid filename."""
        url = re.sub(r'https?://', '', url)
        url = re.sub(r'[\\/*?:"<>|]', '_', url)
        url = url.replace('/', '_').replace('.', '_')
        return url[:100]

    async def analyze_url(self, url: str) -> Dict[str, Any]:
        output_dir = "output"
        os.makedirs(output_dir, exist_ok=True)
        result = {}

        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            try:
                response = requests.get(url, headers=headers, timeout=10, verify=True)
                response.raise_for_status()
            except requests.exceptions.SSLError:
                response = requests.get(url, headers=headers, timeout=10, verify=False)
                response.raise_for_status()
            html_content = response.text
            base_url = response.url
            result = await self.select_prefetch_links(html_content, base_url)
            result["source_url"] = url
        except Exception as e:
            result = {
                "error": str(e),
                "source_url": url,
                "selected_links": [],
                "reasoning": f"Failed to analyze URL: {str(e)}",
                "consistency_hash": "",
                "confidence_score": 0.0
            }
        finally:
            if result:
                model_name = self.model.id.replace('/', '_').replace(':', '-')
                cleaned_url = self._clean_url_for_filename(url)
                filename = f"{model_name}_{cleaned_url}.json"
                filepath = os.path.join(output_dir, filename)

                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=4)
                print(f"Output saved to {filepath}")

        return result 