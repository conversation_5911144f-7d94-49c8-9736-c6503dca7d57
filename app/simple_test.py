#!/usr/bin/env python3
"""
Simple test to verify tool functionality
"""

# Test the tools directly
def test_tools():
    print("🧪 Testing individual tools...")
    
    # Test the global tool functions
    try:
        from agent import retrieve_rag_context, generate_rag_query, analyze_link_patterns
        print("✅ Tools imported successfully")
        
        # Test generate_rag_query
        print("\n🔧 Testing generate_rag_query tool...")
        query_result = generate_rag_query(
            analysis_context="testing 5 links on a sample page",
            specific_need="criteria for selecting clickable links"
        )
        print(f"Query result: {query_result[:100]}...")
        
        # Test analyze_link_patterns
        print("\n🔧 Testing analyze_link_patterns tool...")
        sample_links = """1. URL: https://example.com/home
   Text: Home
   Button Type: navigation
   Location: navigation

2. URL: https://example.com/shop
   Text: Shop Now
   Button Type: call-to-action
   Location: main"""
        
        pattern_result = analyze_link_patterns(sample_links)
        print(f"Pattern analysis: {pattern_result[:100]}...")
        
        print("\n✅ All tools working correctly!")
        
    except Exception as e:
        print(f"❌ Tool test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tools()
