---
sidebar_position: 3
slug: /implement_deep_research
---

# Implement deep research

Implements deep research for agentic reasoning.

---

From v0.17.0 onward, RAGFlow supports integrating agentic reasoning in an AI chat. The following diagram illustrates the workflow of <PERSON><PERSON><PERSON><PERSON>'s deep research:

![Image](https://github.com/user-attachments/assets/f65d4759-4f09-4d9d-9549-c0e1fe907525)

To activate this feature:

1. Enable the **Reasoning** toggle under the **Prompt engine** tab of your chat assistant dialogue.

![Image](https://github.com/user-attachments/assets/4a1968d0-0128-4371-879f-77f3a70197f5)

2. Enter the correct Tavily API key under the **Assistant settings** tab of your chat assistant dialogue to leverage Tavily-based web search

![Image](https://github.com/user-attachments/assets/e8787532-7e72-49ef-8951-169ae544512f)

*The following is a screenshot of a conversation that integrates Deep Research:*

![Image](https://github.com/user-attachments/assets/165b88ff-1f5d-4fb8-90e2-c836b25e32e9)