---
sidebar_position: 8
slug: /rewrite_component
---

# Rewrite component

A component that rewrites a user query.

---

A **Rewrite** component uses a specified LLM to rewrite a user query from the **Interact** component, based on the context of previous dialogues.

## Scenarios

A **Rewrite** component is essential when you need to optimize a user query based on the context of previous conversations. It is usually the upstream component of a **Retrieval** component.

:::tip NOTE
See also the [Keyword](./keyword.mdx) component, a similar component used for multi-turn optimization.
:::

## Configurations

:::tip NOTE
The **Rewrite** component uses the user-agent interaction from the **Interact** component as its data input. Therefore, there is no need to specify its data inputs in the Configurations.
:::

### Model

Click the dropdown menu of **Model** to show the model configuration window.

- **Model**: The chat model to use.  
  - Ensure you set the chat model correctly on the **Model providers** page.
  - You can use different models for different components to increase flexibility or improve overall performance.
- **Freedom**: A shortcut to **Temperature**, **Top P**, **Presence penalty**, and **Frequency penalty** settings, indicating the freedom level of the model. From **Improvise**, **Precise**, to **Balance**, each preset configuration corresponds to a unique combination of **Temperature**, **Top P**, **Presence penalty**, and **Frequency penalty**.   
  This parameter has three options:
  - **Improvise**: Produces more creative responses.
  - **Precise**: (Default) Produces more conservative responses.
  - **Balance**: A middle ground between **Improvise** and **Precise**.
- **Temperature**: The randomness level of the model's output.  
  Defaults to 0.1.
  - Lower values lead to more deterministic and predictable outputs.
  - Higher values lead to more creative and varied outputs.
  - A temperature of zero results in the same output for the same prompt.
- **Top P**: Nucleus sampling.  
  - Reduces the likelihood of generating repetitive or unnatural text by setting a threshold *P* and restricting the sampling to tokens with a cumulative probability exceeding *P*.
  - Defaults to 0.3.
- **Presence penalty**: Encourages the model to include a more diverse range of tokens in the response.  
  - A higher **presence penalty** value results in the model being more likely to generate tokens not yet been included in the generated text.
  - Defaults to 0.4.
- **Frequency penalty**: Discourages the model from repeating the same words or phrases too frequently in the generated text.  
  - A higher **frequency penalty** value results in the model being more conservative in its use of repeated tokens.
  - Defaults to 0.7.

:::tip NOTE
- It is not necessary to stick with the same model for all components. If a specific model is not performing well for a particular task, consider using a different one.
- If you are uncertain about the mechanism behind **Temperature**, **Top P**, **Presence penalty**, and **Frequency penalty**, simply choose one of the three options of **Preset configurations**.
:::


### Message window size

An integer specifying the number of previous dialogue rounds to input into the LLM. For example, if it is set to 12, the tokens from the last 12 dialogue rounds will be fed to the LLM. This feature consumes additional tokens.

Defaults to 1.

:::tip IMPORTANT
This feature is used for multi-turn dialogue *only*. If your **Categorize** component is not part of a multi-turn dialogue (i.e., it is not in a loop), leave this field as-is.
:::

## Examples

Explore our customer service agent template, where the **Rewrite** component (component ID: **Refine Question**) is used to optimize a product-specific user query based on context of previous dialogues before passing it on to the **Retrieval** component.

1. Click the **Agent** tab at the top center of the page to access the **Agent** page.
2. Click **+ Create agent** on the top right of the page to open the **agent template** page.
3. On the **agent template** page, hover over the **Customer service** card and click **Use this template**.
4. Name your new agent and click **OK** to enter the workflow editor.
5. Click on the **Rewrite** component to display its **Configuration** window.