---
sidebar_position: 0
slug: /glossary
---

# Glossary

Definitions of key terms and basic concepts related to RAGFlow.

---

import TOCInline from '@theme/TOCInline';

<TOCInline toc={toc} />

---

## C

### Cross-language search

Cross-language search (also known as cross-lingual retrieval) is a feature introduced in version 0.19.1. It enables users to submit queries in one language (for example, English) and retrieve relevant documents written in other languages such as Chinese or Spanish. This feature is enabled by the system’s default chat model, which translates queries to ensure accurate matching of semantic meaning across languages.

By enabling cross-language search, users can effortlessly access a broader range of information regardless of language barriers, significantly enhancing the system’s usability and inclusiveness.

This feature is available in the retrieval test and chat assistant settings. See [Run retrieval test](../guides/dataset/run_retrieval_test.md) and [Start AI chat](../guides/chat/start_chat.md) for further details.