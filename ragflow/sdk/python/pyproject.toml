[project]
name = "ragflow-sdk"
version = "0.19.1"
description = "Python client sdk of [RAGFlow](https://github.com/infiniflow/ragflow). RAGFlow is an open-source RAG (Retrieval-Augmented Generation) engine based on deep document understanding."
authors = [{ name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>" }]
license = { text = "Apache License, Version 2.0" }
readme = "README.md"
requires-python = ">=3.10,<3.13"
dependencies = ["requests>=2.30.0,<3.0.0", "beartype>=0.18.5,<0.19.0"]


[dependency-groups]
test = [
    "hypothesis>=6.131.9",
    "openpyxl>=3.1.5",
    "pillow>=11.1.0",
    "pytest>=8.3.5",
    "python-docx>=1.1.2",
    "python-pptx>=1.0.2",
    "reportlab>=4.3.1",
    "requests>=2.32.3",
    "requests-toolbelt>=1.0.0",
]


[tool.pytest.ini_options]
markers = [
    "p1: high priority test cases",
    "p2: medium priority test cases",
    "p3: low priority test cases",
]
