{"defaultAction": "SCMP_ACT_ERRNO", "archMap": [{"architecture": "SCMP_ARCH_X86_64", "subArchitectures": ["SCMP_ARCH_X86", "SCMP_ARCH_X32"]}], "syscalls": [{"names": ["read", "write", "exit", "sigret<PERSON>", "brk", "mmap", "mun<PERSON>p", "rt_sigaction", "rt_sigprocmask", "futex", "clone", "execve", "arch_prctl", "access", "openat", "close", "stat", "fstat", "lstat", "getpid", "gettid", "getuid", "getgid", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "clock_gettime", "nanosleep", "uname", "writev", "readlink", "getrandom", "statx", "faccessat2", "pread64", "pwrite64", "rt_sigreturn"], "action": "SCMP_ACT_ALLOW"}]}