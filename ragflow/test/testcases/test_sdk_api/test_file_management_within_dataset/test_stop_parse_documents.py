#
#  Copyright 2025 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import pytest


def validate_document_parse_done(dataset, document_ids):
    documents = dataset.list_documents(page_size=1000)
    for document in documents:
        if document.id in document_ids:
            assert document.run == "DONE"
            assert len(document.process_begin_at) > 0
            assert document.process_duation > 0
            assert document.progress > 0
            assert "Task done" in document.progress_msg


def validate_document_parse_cancel(dataset, document_ids):
    documents = dataset.list_documents(page_size=1000)
    for document in documents:
        assert document.run == "CANCEL"
        assert len(document.process_begin_at) > 0
        assert document.progress == 0.0


@pytest.mark.skip
class TestDocumentsParseStop:
    pass
