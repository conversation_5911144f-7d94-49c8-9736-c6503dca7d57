services:
  ragflow:
    build:
      context: ./ragflow
      dockerfile: Dockerfile
    container_name: ragflow-server
    ports:
      - "80:80"
      - "443:443"
      - "9380:9380"
    volumes:
      - ./ragflow/ragflow-logs:/ragflow/logs
      - ./ragflow/history_data_agent:/ragflow/history_data_agent
      - ./ragflow/docker/service_conf.yaml.template:/ragflow/conf/service_conf.yaml.template
      - ./ragflow/docker/nginx/ragflow.conf:/etc/nginx/conf.d/ragflow.conf
      - ./ragflow/docker/nginx/proxy.conf:/etc/nginx/proxy.conf
      - ./ragflow/docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./ragflow/docker/init.sql:/data/application/init.sql
    env_file: .env
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_started
      minio:
        condition: service_started
      es01:
        condition: service_healthy
    networks:
      - ragflow
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9380/api/v1/health || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 30
    restart: on-failure

  mysql:
    image: mysql:8.0.39
    container_name: ragflow-mysql
    env_file: .env
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_PASSWORD}
      - TZ=${TIMEZONE}
    command:
      --max_connections=1000
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --default-authentication-plugin=mysql_native_password
      --tls_version="TLSv1.2,TLSv1.3"
      --init-file /data/application/init.sql
      --binlog_expire_logs_seconds=604800
    ports:
      - "${MYSQL_PORT}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./ragflow/docker/init.sql:/data/application/init.sql
    networks:
      - ragflow
    healthcheck:
      test: ["CMD", "mysqladmin" ,"ping", "-uroot", "-p${MYSQL_PASSWORD}"]
      interval: 10s
      timeout: 10s
      retries: 30
    restart: on-failure

  minio:
    image: quay.io/minio/minio:RELEASE.2023-12-20T01-00-02Z
    container_name: ragflow-minio
    command: server --console-address ":9001" /data
    ports:
      - "${MINIO_PORT}:9000"
      - "${MINIO_CONSOLE_PORT}:9001"
    env_file: .env
    environment:
      - MINIO_ROOT_USER=${MINIO_USER}
      - MINIO_ROOT_PASSWORD=${MINIO_PASSWORD}
      - TZ=${TIMEZONE}
    volumes:
      - minio_data:/data
    networks:
      - ragflow
    restart: on-failure

  redis:
    image: valkey/valkey:8
    container_name: ragflow-redis
    command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 128mb --maxmemory-policy allkeys-lru
    env_file: .env
    ports:
      - "${REDIS_PORT}:6379"
    volumes:
      - redis_data:/data
    networks:
      - ragflow
    restart: on-failure

  es01:
    container_name: ragflow-es-01
    image: elasticsearch:8.6.2
    volumes:
      - es_data:/usr/share/elasticsearch/data
    ports:
      - "${ES_PORT}:9200"
    env_file: .env
    environment:
      - node.name=es01
      - ELASTIC_PASSWORD=${ELASTIC_PASSWORD}
      - bootstrap.memory_lock=false
      - discovery.type=single-node
      - xpack.security.enabled=true
      - xpack.security.http.ssl.enabled=false
      - xpack.security.transport.ssl.enabled=false
      - TZ=${TIMEZONE}
    ulimits:
      memlock:
        soft: -1
        hard: -1
    healthcheck:
      test: ["CMD-SHELL", "curl -s -u elastic:${ELASTIC_PASSWORD} http://localhost:9200"]
      interval: 10s
      timeout: 10s
      retries: 120
    networks:
      - ragflow
    restart: on-failure

  link-prefetch-agent:
    build:
      context: ./app
      dockerfile: Dockerfile
    container_name: link-prefetch-agent
    ports:
      - "7860:7860"
    volumes:
      - ./app/output:/usr/src/app/output 
    networks:
      - ragflow
    environment:
      - AGNO_API_KEY=ag-6qjdhBjWjaoeYuMGG2Mtb4NeEIv4QcHB5bN7zKa3dlU
      - AGNO_MONITOR=true
    depends_on:
      ragflow:
        condition: service_healthy
    restart: on-failure

volumes:
  es_data:
  mysql_data:
  minio_data:
  redis_data:

networks:
  ragflow:
    driver: bridge 